{"rustc": 13226066032359371072, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15599109589607159429, "path": 13260424223930269531, "deps": [[5820056977320921005, "anstream", false, 5385368437545194256], [9394696648929125047, "anstyle", false, 12161081149465206282], [11166530783118767604, "strsim", false, 1720234103969682592], [11649982696571033535, "clap_lex", false, 1582850329012523951]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-130bf3edff33c381/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}