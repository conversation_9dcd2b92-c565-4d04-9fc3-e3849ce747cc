[package]
name = "solana-grpc-monitor"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tonic = { version = "0.11", features = ["tls"] }
prost = "0.12"
yellowstone-grpc-client = "6.1.0"
yellowstone-grpc-proto = "6.1.0"
solana-sdk = "2.2.1"
bs58 = "0.5"
# 使用更兼容的sqlx版本，启用postgres和macros功能
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "macros"], default-features = false }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
futures = "0.3"

# 强制指定zeroize版本以解决冲突
[dependencies.zeroize]
version = "1.3"
